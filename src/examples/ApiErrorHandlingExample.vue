<template>
  <div class="api-error-example">
    <h3>API错误处理示例</h3>
    
    <el-card class="example-card">
      <h4>普通API调用</h4>
      <el-button @click="normalApiCall" type="primary">
        调用普通API
      </el-button>
    </el-card>

    <el-card class="example-card">
      <h4>带重试机制的API调用</h4>
      <el-button @click="apiCallWithRetry" type="success">
        调用带重试的API
      </el-button>
    </el-card>

    <el-card class="example-card">
      <h4>文件上传（自动处理504错误）</h4>
      <el-upload
        :action="uploadUrl"
        :before-upload="beforeUpload"
        :on-success="onUploadSuccess"
        :on-error="onUploadError"
        :show-file-list="false"
      >
        <el-button type="warning">上传文件</el-button>
      </el-upload>
    </el-card>

    <el-card class="example-card">
      <h4>数据导出（自动处理504错误）</h4>
      <el-button @click="exportData" type="info">
        导出数据
      </el-button>
    </el-card>
  </div>
</template>

<script>
import { dataApi, fileApi } from '@/api'
import { createApiCallWithErrorHandler } from '@/utils/errorHandler'

export default {
  name: 'ApiErrorHandlingExample',
  data() {
    return {
      uploadUrl: '/api/file/upload'
    }
  },
  methods: {
    // 普通API调用
    async normalApiCall() {
      try {
        const result = await dataApi.getDataList()
        this.$message.success('API调用成功')
        console.log('结果:', result)
      } catch (error) {
        console.error('API调用失败:', error)
      }
    },

    // 带重试机制的API调用
    async apiCallWithRetry() {
      // 创建带错误处理的API调用
      const apiCallWithHandler = createApiCallWithErrorHandler(
        dataApi.getDataList,
        {
          maxRetries: 3,
          retryDelay: 2000,
          showRetryDialog: true
        }
      )

      try {
        const result = await apiCallWithHandler()
        this.$message.success('API调用成功')
        console.log('结果:', result)
      } catch (error) {
        console.error('API调用最终失败:', error)
      }
    },

    // 文件上传前检查
    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!')
        return false
      }
      return true
    },

    // 上传成功
    onUploadSuccess(response, file) {
      this.$message.success('文件上传成功')
      console.log('上传结果:', response)
    },

    // 上传失败
    onUploadError(error, file) {
      console.error('上传失败:', error)
      if (error.status === 504) {
        this.$message.error('文件上传超时，请检查网络连接或稍后重试')
      }
    },

    // 数据导出
    async exportData() {
      try {
        this.$message.info('开始导出数据，请稍候...')
        
        // 使用带重试机制的导出API
        const result = await dataApi.exportData({
          format: 'excel',
          dateRange: '2024-01-01,2024-12-31'
        })
        
        this.$message.success('数据导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        if (error.response?.status === 504) {
          this.$confirm(
            '数据导出超时，可能是因为数据量较大。是否重试？',
            '导出超时',
            {
              confirmButtonText: '重试',
              cancelButtonText: '取消',
              type: 'warning'
            }
          ).then(() => {
            this.exportData() // 重试
          })
        }
      }
    }
  }
}
</script>

<style scoped>
.api-error-example {
  padding: 20px;
}

.example-card {
  margin-bottom: 20px;
}

.example-card h4 {
  margin-bottom: 15px;
  color: #409EFF;
}
</style>
