import axios from 'axios'

/**
 * 代理健康检查工具
 */
class ProxyHealthChecker {
  constructor() {
    this.isHealthy = true
    this.lastCheckTime = null
    this.checkInterval = 30000 // 30秒检查一次
    this.retryAttempts = 3
    this.retryDelay = 2000
    this.healthCheckUrl = '/api/health' // 健康检查端点
    
    // 启动定期健康检查
    this.startHealthCheck()
  }

  /**
   * 启动定期健康检查
   */
  startHealthCheck() {
    setInterval(() => {
      this.checkHealth()
    }, this.checkInterval)
  }

  /**
   * 检查代理健康状态
   */
  async checkHealth() {
    try {
      const response = await axios.get(this.healthCheckUrl, {
        timeout: 5000,
        headers: {
          'Cache-Control': 'no-cache'
        }
      })
      
      this.isHealthy = response.status === 200
      this.lastCheckTime = new Date()
      
      if (this.isHealthy) {
        console.log('代理健康检查通过')
      }
    } catch (error) {
      this.isHealthy = false
      this.lastCheckTime = new Date()
      console.warn('代理健康检查失败:', error.message)
    }
  }

  /**
   * 获取健康状态
   */
  getHealthStatus() {
    return {
      isHealthy: this.isHealthy,
      lastCheckTime: this.lastCheckTime,
      status: this.isHealthy ? 'healthy' : 'unhealthy'
    }
  }

  /**
   * 带重试的API请求
   */
  async requestWithRetry(requestFn, options = {}) {
    const {
      maxRetries = this.retryAttempts,
      retryDelay = this.retryDelay,
      onRetry = null
    } = options

    let lastError = null

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await requestFn()
      } catch (error) {
        lastError = error
        
        // 如果是最后一次尝试，直接抛出错误
        if (attempt === maxRetries) {
          break
        }

        // 检查是否是可重试的错误
        if (this.isRetryableError(error)) {
          console.log(`请求失败，第${attempt + 1}次重试...`, error.message)
          
          if (onRetry) {
            onRetry(attempt + 1, error)
          }
          
          // 等待后重试
          await this.delay(retryDelay * (attempt + 1))
        } else {
          // 不可重试的错误，直接抛出
          break
        }
      }
    }

    throw lastError
  }

  /**
   * 判断是否是可重试的错误
   */
  isRetryableError(error) {
    if (!error.response) {
      // 网络错误、超时等
      return error.code === 'ECONNABORTED' || 
             error.code === 'ENOTFOUND' ||
             error.code === 'ECONNRESET' ||
             error.message.includes('Network Error')
    }

    // HTTP状态码错误
    const retryableStatusCodes = [502, 503, 504, 408, 429]
    return retryableStatusCodes.includes(error.response.status)
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 创建带健康检查的axios实例
   */
  createAxiosInstance(config = {}) {
    const instance = axios.create({
      timeout: 30000,
      ...config
    })

    // 请求拦截器
    instance.interceptors.request.use(
      (config) => {
        // 添加健康状态头
        config.headers['X-Proxy-Health'] = this.isHealthy ? 'healthy' : 'unhealthy'
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    instance.interceptors.response.use(
      (response) => {
        // 请求成功，标记为健康
        this.isHealthy = true
        return response
      },
      (error) => {
        // 检查是否是代理相关错误
        if (this.isRetryableError(error)) {
          this.isHealthy = false
        }
        return Promise.reject(error)
      }
    )

    return instance
  }
}

// 创建全局实例
const proxyHealthChecker = new ProxyHealthChecker()

export default proxyHealthChecker
export { ProxyHealthChecker }
