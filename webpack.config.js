const path = require('path')
const webpack = require('webpack')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const VueLoaderPlugin = require('vue-loader/lib/plugin')
const Dotenv = require('dotenv-webpack')

module.exports = {
  entry: './src/main.js',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'bundle.js',
    publicPath: '/'
  },
  module: {
    rules: [
      {
        test: /\.vue$/,
        loader: 'vue-loader'
      },
      {
        test: /\.js$/,
        loader: 'babel-loader',
        exclude: /node_modules/
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader']
      },
      {
        test: /\.(scss|sass)$/,
        use: ['style-loader', 'css-loader', 'sass-loader']
      },
      {
        test: /\.(png|jpg|gif|svg)$/,
        type: 'asset/resource',
        generator: {
          filename: 'images/[name].[hash:8][ext]'
        }
      },
      {
        test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
        type: 'asset/resource',
        generator: {
          filename: 'fonts/[name].[hash:8][ext]'
        }
      }
    ]
  },
  plugins: [
    new VueLoaderPlugin(),
    new HtmlWebpackPlugin({
      template: './public/index.html',
      filename: 'index.html'
    }),
    new Dotenv({
      path: `./.env.${process.env.NODE_ENV || 'development'}`,
      safe: false,
      systemvars: true,
      silent: false
    })
  ],
  resolve: {
    alias: {
      vue$: 'vue/dist/vue.esm.js',
      '@': path.resolve(__dirname, 'src')
    },
    extensions: ['*', '.js', '.vue', '.json'],
    fallback: {
      process: require.resolve('process/browser')
    }
  },
  devServer: {
    historyApiFallback: true,
    client: {
      overlay: true
    },
    host: 'localhost',
    port: 8081,
    open: true,
    hot: true,
    static: {
      directory: path.join(__dirname, 'public'),
      publicPath: '/'
    },
    proxy: {
      '/api': {
        target: 'http://**************:3011',
        changeOrigin: true,
        secure: false,
        logLevel: 'debug',
        timeout: 60000, // 增加到60秒
        proxyTimeout: 60000, // 增加到60秒
        followRedirects: true,
        headers: {
          'Connection': 'keep-alive',
          'Keep-Alive': 'timeout=60, max=1000'
        },
        pathRewrite: {
          '^/api': '/api'  // 将 /api 转换为 /api
        },
        onError: function (err, req, res) {
          console.error('代理错误详情:', {
            message: err.message,
            code: err.code,
            url: req.url,
            method: req.method,
            timestamp: new Date().toISOString()
          });

          // 返回友好的错误响应
          if (!res.headersSent) {
            res.writeHead(502, {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            });
            res.end(JSON.stringify({
              Ret: 0,
              Msg: '代理服务器错误: ' + err.message,
              Data: null,
              error: {
                type: 'PROXY_ERROR',
                code: err.code,
                timestamp: new Date().toISOString()
              }
            }));
          }
        },
        onProxyReq: function (proxyReq, req, res) {
          console.log('代理请求:', {
            method: req.method,
            url: req.url,
            headers: req.headers,
            timestamp: new Date().toISOString()
          });

          // 设置更长的超时时间
          proxyReq.setTimeout(60000, function() {
            console.error('代理请求超时:', req.url);
            proxyReq.abort();
          });
        },
        onProxyRes: function (proxyRes, req, res) {
          console.log('代理响应:', {
            statusCode: proxyRes.statusCode,
            url: req.url,
            headers: proxyRes.headers,
            timestamp: new Date().toISOString()
          });

          // 添加CORS头
          res.setHeader('Access-Control-Allow-Origin', '*');
          res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
          res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Token');
        }
      }
    }
  },
  performance: {
    hints: false
  },
  devtool: 'eval-source-map'
}

if (process.env.NODE_ENV === 'production') {
  module.exports.devtool = 'source-map'
  module.exports.optimization = {
    minimize: true,
    minimizer: [
      new (require('terser-webpack-plugin'))({
        terserOptions: {
          compress: {
            warnings: false
          }
        }
      })
    ]
  }
  module.exports.plugins = (module.exports.plugins || []).concat([
    new webpack.DefinePlugin({
      'process.env': {
        NODE_ENV: '"production"'
      }
    })
  ])
}
