<template>
  <div class="proxy-status">
    <el-card class="status-card">
      <div slot="header" class="card-header">
        <span>代理服务状态</span>
        <el-button 
          type="text" 
          @click="refreshStatus"
          :loading="refreshing"
          icon="el-icon-refresh"
        >
          刷新
        </el-button>
      </div>
      
      <div class="status-content">
        <div class="status-item">
          <span class="label">连接状态:</span>
          <el-tag 
            :type="healthStatus.isHealthy ? 'success' : 'danger'"
            :icon="healthStatus.isHealthy ? 'el-icon-success' : 'el-icon-error'"
          >
            {{ healthStatus.status === 'healthy' ? '正常' : '异常' }}
          </el-tag>
        </div>
        
        <div class="status-item">
          <span class="label">最后检查:</span>
          <span class="value">
            {{ healthStatus.lastCheckTime ? formatTime(healthStatus.lastCheckTime) : '未检查' }}
          </span>
        </div>
        
        <div class="status-item">
          <span class="label">API地址:</span>
          <span class="value">{{ apiBaseUrl }}</span>
        </div>
        
        <div class="status-item">
          <span class="label">超时设置:</span>
          <span class="value">{{ timeoutSetting }}ms</span>
        </div>
      </div>
      
      <div class="action-buttons">
        <el-button 
          type="primary" 
          size="small" 
          @click="testConnection"
          :loading="testing"
        >
          测试连接
        </el-button>
        
        <el-button 
          type="warning" 
          size="small" 
          @click="showProxyLogs"
        >
          查看日志
        </el-button>
      </div>
    </el-card>

    <!-- 连接测试结果对话框 -->
    <el-dialog
      title="连接测试结果"
      :visible.sync="testDialogVisible"
      width="600px"
    >
      <div class="test-results">
        <div v-for="(result, index) in testResults" :key="index" class="test-item">
          <div class="test-header">
            <span class="test-name">{{ result.name }}</span>
            <el-tag 
              :type="result.success ? 'success' : 'danger'"
              size="small"
            >
              {{ result.success ? '成功' : '失败' }}
            </el-tag>
          </div>
          <div class="test-details">
            <p><strong>耗时:</strong> {{ result.duration }}ms</p>
            <p><strong>状态:</strong> {{ result.status || '无响应' }}</p>
            <p v-if="result.error"><strong>错误:</strong> {{ result.error }}</p>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 代理日志对话框 -->
    <el-dialog
      title="代理日志"
      :visible.sync="logsDialogVisible"
      width="800px"
    >
      <div class="logs-content">
        <el-input
          type="textarea"
          :rows="15"
          v-model="proxyLogs"
          readonly
          placeholder="暂无日志信息"
        />
      </div>
      <div slot="footer">
        <el-button @click="clearLogs">清空日志</el-button>
        <el-button type="primary" @click="logsDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'
import { projectStructureApi } from '@/api'

export default {
  name: 'ProxyStatus',
  data() {
    return {
      healthStatus: {
        isHealthy: true,
        lastCheckTime: null,
        status: 'unknown'
      },
      refreshing: false,
      testing: false,
      testDialogVisible: false,
      logsDialogVisible: false,
      testResults: [],
      proxyLogs: '',
      apiBaseUrl: '',
      timeoutSetting: 30000
    }
  },
  mounted() {
    this.loadStatus()
    this.loadConfig()
    
    // 定期更新状态
    this.statusTimer = setInterval(() => {
      this.loadStatus()
    }, 10000) // 每10秒更新一次
  },
  beforeDestroy() {
    if (this.statusTimer) {
      clearInterval(this.statusTimer)
    }
  },
  methods: {
    loadStatus() {
      try {
        this.healthStatus = request.getProxyHealth()
      } catch (error) {
        console.error('获取代理状态失败:', error)
      }
    },
    
    loadConfig() {
      const config = window.getConfig ? window.getConfig() : {}
      this.apiBaseUrl = config.baseURL || '/api'
      this.timeoutSetting = config.timeout || 30000
    },
    
    async refreshStatus() {
      this.refreshing = true
      try {
        // 强制刷新状态
        await new Promise(resolve => setTimeout(resolve, 1000))
        this.loadStatus()
        this.$message.success('状态已刷新')
      } catch (error) {
        this.$message.error('刷新失败: ' + error.message)
      } finally {
        this.refreshing = false
      }
    },
    
    async testConnection() {
      this.testing = true
      this.testResults = []
      
      const tests = [
        {
          name: '基础连接测试',
          test: () => request.get('/system/config')
        },
        {
          name: '项目结构API测试',
          test: () => projectStructureApi.getStructureTree({
            parentId: '0',
            organizeId: window.getProjectId ? window.getProjectId() : ''
          })
        },
        {
          name: '用户信息API测试',
          test: () => request.get('/user/info')
        }
      ]
      
      for (const testCase of tests) {
        const startTime = Date.now()
        try {
          const response = await testCase.test()
          const duration = Date.now() - startTime
          
          this.testResults.push({
            name: testCase.name,
            success: true,
            duration,
            status: response.status || 200
          })
        } catch (error) {
          const duration = Date.now() - startTime
          
          this.testResults.push({
            name: testCase.name,
            success: false,
            duration,
            status: error.response?.status,
            error: error.message
          })
        }
      }
      
      this.testing = false
      this.testDialogVisible = true
    },
    
    showProxyLogs() {
      // 这里可以从localStorage或其他地方获取日志
      this.proxyLogs = this.getStoredLogs()
      this.logsDialogVisible = true
    },
    
    getStoredLogs() {
      // 模拟获取日志，实际项目中可以从localStorage或API获取
      return `[${new Date().toISOString()}] 代理服务启动
[${new Date().toISOString()}] 目标服务器: http://**************:3011
[${new Date().toISOString()}] 代理状态: ${this.healthStatus.isHealthy ? '正常' : '异常'}
[${new Date().toISOString()}] 最后检查时间: ${this.healthStatus.lastCheckTime || '未知'}`
    },
    
    clearLogs() {
      this.proxyLogs = ''
      this.$message.success('日志已清空')
    },
    
    formatTime(time) {
      if (!time) return '未知'
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.proxy-status {
  margin: 20px 0;
}

.status-card {
  max-width: 600px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-content {
  margin-bottom: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.label {
  font-weight: bold;
  margin-right: 10px;
  min-width: 80px;
}

.value {
  color: #606266;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.test-results {
  max-height: 400px;
  overflow-y: auto;
}

.test-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 10px;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.test-name {
  font-weight: bold;
}

.test-details p {
  margin: 5px 0;
  font-size: 14px;
}

.logs-content {
  margin-bottom: 20px;
}
</style>
