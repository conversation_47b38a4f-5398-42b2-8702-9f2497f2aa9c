import { Message, MessageBox } from 'element-ui'

/**
 * 处理504网关超时错误
 * @param {Error} error - axios错误对象
 * @param {Function} retryCallback - 重试回调函数
 * @param {Object} options - 配置选项
 */
export function handle504Error(error, retryCallback, options = {}) {
  const {
    maxRetries = 3,
    showRetryDialog = true,
    autoRetry = false,
    retryDelay = 2000
  } = options

  if (error.response?.status === 504) {
    if (autoRetry && retryCallback) {
      // 自动重试
      setTimeout(() => {
        console.log('检测到504错误，自动重试中...')
        retryCallback()
      }, retryDelay)
      return true
    }

    if (showRetryDialog && retryCallback) {
      // 显示重试对话框
      MessageBox.confirm(
        '请求超时，服务器响应时间过长。是否重试？',
        '网络超时',
        {
          confirmButtonText: '重试',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        retryCallback()
      }).catch(() => {
        Message({
          message: '请求已取消',
          type: 'info'
        })
      })
      return true
    }

    // 显示友好的错误提示
    Message({
      message: '服务器响应超时，请稍后重试或联系管理员',
      type: 'error',
      duration: 5000
    })
    return true
  }

  return false
}

/**
 * 创建带有504错误处理的API调用包装器
 * @param {Function} apiCall - API调用函数
 * @param {Object} options - 配置选项
 */
export function createApiCallWithErrorHandler(apiCall, options = {}) {
  const {
    maxRetries = 3,
    retryDelay = 2000,
    showRetryDialog = true
  } = options

  let retryCount = 0

  const executeCall = async (...args) => {
    try {
      return await apiCall(...args)
    } catch (error) {
      if (error.response?.status === 504 && retryCount < maxRetries) {
        retryCount++
        
        if (showRetryDialog) {
          const shouldRetry = await MessageBox.confirm(
            `请求超时（第${retryCount}次），是否重试？`,
            '网络超时',
            {
              confirmButtonText: '重试',
              cancelButtonText: '取消',
              type: 'warning'
            }
          ).catch(() => false)

          if (shouldRetry) {
            console.log(`第${retryCount}次重试...`)
            await new Promise(resolve => setTimeout(resolve, retryDelay))
            return executeCall(...args)
          }
        } else {
          // 自动重试
          console.log(`检测到504错误，第${retryCount}次自动重试...`)
          await new Promise(resolve => setTimeout(resolve, retryDelay))
          return executeCall(...args)
        }
      }
      
      throw error
    }
  }

  return executeCall
}

/**
 * 网络状态检查
 */
export function checkNetworkStatus() {
  if (!navigator.onLine) {
    Message({
      message: '网络连接已断开，请检查网络设置',
      type: 'error',
      duration: 5000
    })
    return false
  }
  return true
}

/**
 * 获取错误的友好提示信息
 * @param {Error} error - 错误对象
 */
export function getErrorMessage(error) {
  if (!error.response) {
    if (error.code === 'ECONNABORTED') {
      return '请求超时，请检查网络连接'
    }
    if (error.message.includes('Network Error')) {
      return '网络连接异常，请检查网络设置'
    }
    return '网络错误，请稍后重试'
  }

  const { status } = error.response
  const errorMessages = {
    400: '请求参数错误',
    401: '未授权，请重新登录',
    403: '拒绝访问',
    404: '请求地址不存在',
    500: '服务器内部错误',
    502: '网关错误',
    503: '服务不可用',
    504: '网关超时，服务器响应时间过长'
  }

  return errorMessages[status] || `连接错误 ${status}`
}
