import axios from 'axios'
import { Message, Loading } from 'element-ui'
import store from '@/store'
import router from '@/router'
import { getErrorMessage } from './errorHandler'
import proxyHealthChecker from './proxyHealthCheck'

// 获取环境配置
const config = window.getConfig
  ? window.getConfig()
  : {
    baseURL: '/api',
    timeout: 1000 * 60 *10 // 增加到30秒
  }

// 创建axios实例
const service = axios.create({
  baseURL: config.baseURL,
  timeout: config.timeout,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  },
  // 添加重试配置
  retry: 3,
  retryDelay: 2000
})

// loading实例
let loadingInstance = null
let requestCount = 0

// 显示loading
function showLoading(config) {
  if (config.loading !== false) {
    requestCount++
    if (requestCount === 1) {
      loadingInstance = Loading.service({
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
    }
  }
}

// 隐藏loading
function hideLoading() {
  requestCount--
  if (requestCount === 0 && loadingInstance) {
    loadingInstance.close()
    loadingInstance = null
  }
}

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 显示loading
    showLoading(config)

    // 添加token
    const token = store.getters.token || localStorage.getItem('token')
    if (token) {
      config.headers.Token =  token
    }

    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    return config
  },
  error => {
    hideLoading()
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    hideLoading()

    const res = response.data
    // 根据后端返回的状态码进行处理
    if (res.Ret !== 1) {
      // 处理业务错误
      Message({
        message: res.Msg || '请求失败',
        type: 'error',
        duration: 3000
      })

      // 401: 未授权，跳转到登录页
      if (res.Ret === 401) {
        store.dispatch('user/logout').then(() => {
          router.push('/login')
        })
      }

      return Promise.reject(new Error(res.Msg || '请求失败'))
    } else {
      return res
    }
  },
  error => {
    hideLoading()

    // 使用统一的错误消息处理
    const message = getErrorMessage(error)

    // 对于504错误，提供更详细的提示
    if (error.response?.status === 504) {
      Message({
        message: '服务器响应超时，请稍后重试。如果问题持续存在，请联系管理员。',
        type: 'error',
        duration: 5000,
        showClose: true
      })
    } else {
      Message({
        message,
        type: 'error',
        duration: 3000
      })
    }

    return Promise.reject(error)
  }
)

// 重试函数
function retryRequest(originalRequest, retryCount = 3, delay = 1000) {
  return new Promise((resolve, reject) => {
    const attempt = (count) => {
      service(originalRequest)
        .then(resolve)
        .catch(error => {
          if (count > 0 && (
            error.response?.status === 504 ||
            error.response?.status === 502 ||
            error.response?.status === 503 ||
            error.code === 'ECONNABORTED'
          )) {
            console.log(`请求失败，${delay}ms后进行第${retryCount - count + 1}次重试...`)
            setTimeout(() => attempt(count - 1), delay)
          } else {
            reject(error)
          }
        })
    }
    attempt(retryCount)
  })
}

// 封装请求方法
const request = {
  // GET请求
  get(url, params = {}, config = {}) {
    return service({
      method: 'get',
      url,
      params,
      ...config
    })
  },

  // POST请求
  post(url, data = {}, config = {}) {
    return service({
      method: 'post',
      url,
      data,
      ...config
    })
  },

  // PUT请求
  put(url, data = {}, config = {}) {
    return service({
      method: 'put',
      url,
      data,
      ...config
    })
  },

  // DELETE请求
  delete(url, params = {}, config = {}) {
    return service({
      method: 'delete',
      url,
      params,
      ...config
    })
  },

  // PATCH请求
  patch(url, data = {}, config = {}) {
    return service({
      method: 'patch',
      url,
      data,
      ...config
    })
  },

  // 文件上传
  upload(url, formData, config = {}) {
    const uploadConfig = {
      method: 'post',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 120000, // 文件上传超时时间设为2分钟
      ...config
    }

    // 对于文件上传，使用重试机制
    if (config.retry !== false) {
      return retryRequest(uploadConfig, 2, 2000) // 重试2次，间隔2秒
    }

    return service(uploadConfig)
  },

  // 文件下载
  download(url, params = {}, config = {}) {
    const downloadConfig = {
      method: 'get',
      url,
      params,
      responseType: 'blob',
      timeout: 120000, // 文件下载超时时间设为2分钟
      ...config
    }

    // 对于文件下载，使用重试机制
    if (config.retry !== false) {
      return retryRequest(downloadConfig, 2, 2000) // 重试2次，间隔2秒
    }

    return service(downloadConfig)
  },

  // 带重试的请求方法
  requestWithRetry(config, retryCount = 3, delay = 1000) {
    return retryRequest(config, retryCount, delay)
  },

  // 带代理健康检查的请求方法
  requestWithHealthCheck(config) {
    return proxyHealthChecker.requestWithRetry(() => service(config), {
      maxRetries: 3,
      retryDelay: 2000,
      onRetry: (attempt, error) => {
        console.log(`代理请求重试 ${attempt}/3:`, error.message)
        Message({
          message: `网络不稳定，正在重试 (${attempt}/3)...`,
          type: 'warning',
          duration: 2000
        })
      }
    })
  },

  // 检查代理健康状态
  getProxyHealth() {
    return proxyHealthChecker.getHealthStatus()
  }
}

export default request
export { service }
