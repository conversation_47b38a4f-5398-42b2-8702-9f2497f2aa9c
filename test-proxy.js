const axios = require('axios')

// 测试代理配置
async function testProxy() {
  console.log('开始测试代理配置...\n')
  
  const tests = [
    {
      name: '直接访问API服务器',
      url: 'http://**************:3011/api/Project/Structure/GetStructureTree',
      params: {
        Token: 'D5318751F71E499BB6D6495D88DBEF27D22C22E9A9F57E3BDDD9153C5AE8D3775B8B0CB49A8CDD9BD6D449895D6EB3F25997E6313A699117433E774C7497D59DD722DA7EEB97A437AA15DEED4C6B190E',
        parentId: '0',
        organizeId: '832a5bf2-fa7e-497b-b849-090c16c02b52'
      }
    },
    {
      name: '通过本地代理访问',
      url: 'http://localhost:8081/api/Project/Structure/GetStructureTree',
      params: {
        Token: 'D5318751F71E499BB6D6495D88DBEF27D22C22E9A9F57E3BDDD9153C5AE8D3775B8B0CB49A8CDD9BD6D449895D6EB3F25997E6313A699117433E774C7497D59DD722DA7EEB97A437AA15DEED4C6B190E',
        parentId: '0',
        organizeId: '832a5bf2-fa7e-497b-b849-090c16c02b52'
      }
    }
  ]
  
  for (const test of tests) {
    console.log(`\n🧪 ${test.name}`)
    console.log(`URL: ${test.url}`)
    
    const startTime = Date.now()
    
    try {
      const response = await axios.get(test.url, {
        params: test.params,
        timeout: 30000,
        headers: {
          'User-Agent': 'Test-Client/1.0'
        }
      })
      
      const duration = Date.now() - startTime
      
      console.log(`✅ 成功`)
      console.log(`   状态码: ${response.status}`)
      console.log(`   耗时: ${duration}ms`)
      console.log(`   数据长度: ${JSON.stringify(response.data).length} 字符`)
      
      if (response.data && response.data.Ret === 1) {
        console.log(`   返回状态: ${response.data.Msg}`)
        console.log(`   数据项数: ${response.data.Data ? response.data.Data.length : 0}`)
      }
      
    } catch (error) {
      const duration = Date.now() - startTime
      
      console.log(`❌ 失败`)
      console.log(`   耗时: ${duration}ms`)
      
      if (error.response) {
        console.log(`   状态码: ${error.response.status}`)
        console.log(`   错误信息: ${error.response.statusText}`)
      } else if (error.code) {
        console.log(`   错误代码: ${error.code}`)
        console.log(`   错误信息: ${error.message}`)
      } else {
        console.log(`   错误信息: ${error.message}`)
      }
    }
  }
  
  console.log('\n📊 测试完成')
}

// 检查开发服务器状态
async function checkDevServer() {
  console.log('\n🔍 检查开发服务器状态...')
  
  try {
    const response = await axios.get('http://localhost:8081', {
      timeout: 5000
    })
    console.log('✅ 开发服务器运行正常')
    return true
  } catch (error) {
    console.log('❌ 开发服务器未运行或不可访问')
    console.log(`   错误: ${error.message}`)
    return false
  }
}

// 主函数
async function main() {
  console.log('🚀 代理配置测试工具\n')
  
  // 检查开发服务器
  const devServerRunning = await checkDevServer()
  
  if (!devServerRunning) {
    console.log('\n💡 请先启动开发服务器:')
    console.log('   npm run dev')
    console.log('\n然后重新运行此测试脚本')
    return
  }
  
  // 运行代理测试
  await testProxy()
  
  console.log('\n💡 如果代理测试失败，请检查:')
  console.log('   1. 开发服务器是否正常运行 (npm run dev)')
  console.log('   2. webpack.config.js 中的代理配置')
  console.log('   3. 目标API服务器是否可访问')
  console.log('   4. 网络连接是否正常')
}

// 运行测试
main().catch(console.error)
