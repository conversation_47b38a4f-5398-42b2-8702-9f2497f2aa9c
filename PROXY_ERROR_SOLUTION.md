# 代理错误解决方案

## 问题描述
```
Error occurred while trying to proxy: localhost:8081/api/Project/Structure/GetStructureTree?Token=...
```

这个错误表明代理服务器在转发请求到后端API时出现了问题。

## 根本原因分析

1. **网络超时**: 后端API响应时间过长，超过了代理的超时设置
2. **代理配置不当**: 超时时间设置过短，错误处理不完善
3. **连接不稳定**: 网络连接不稳定导致间歇性失败

## 解决方案

### 1. 优化代理配置 (webpack.config.js)

已更新的配置包括：
- 增加超时时间到60秒
- 添加连接保持设置
- 改进错误处理和日志记录
- 添加CORS头支持

### 2. 增强请求重试机制 (src/utils/request.js)

- 自动重试504、502、503错误
- 智能重试延迟策略
- 友好的错误提示

### 3. 添加代理健康检查 (src/utils/proxyHealthCheck.js)

- 定期检查代理服务状态
- 自动重试失败的请求
- 提供健康状态监控

### 4. 特殊API优化 (src/api/index.js)

为容易超时的API添加特殊配置：
- 项目结构API: 60秒超时 + 重试机制
- 文件上传/下载: 2分钟超时 + 重试
- 数据导出: 3分钟超时 + 重试

## 使用方法

### 启动开发服务器
```bash
npm run dev
```

### 测试代理配置
```bash
node test-proxy.js
```

### 在组件中使用代理状态监控
```vue
<template>
  <div>
    <ProxyStatus />
    <!-- 其他内容 -->
  </div>
</template>

<script>
import ProxyStatus from '@/components/ProxyStatus.vue'

export default {
  components: {
    ProxyStatus
  }
}
</script>
```

### 使用增强的API调用
```javascript
// 普通调用（自动包含重试机制）
const result = await projectStructureApi.getStructureTree(params)

// 手动使用健康检查
const result = await this.$request.requestWithHealthCheck({
  method: 'get',
  url: '/your-api-endpoint',
  timeout: 60000
})
```

## 故障排除

### 如果代理错误仍然出现：

1. **检查网络连接**
   ```bash
   curl -I http://**************:3011/api/Project/Structure/GetStructureTree
   ```

2. **检查开发服务器状态**
   ```bash
   lsof -i :8081
   ```

3. **查看代理日志**
   - 在浏览器开发者工具的Console中查看详细日志
   - 使用ProxyStatus组件查看代理状态

4. **重启开发服务器**
   ```bash
   # 停止当前服务器
   pkill -f "webpack.*serve"
   
   # 重新启动
   npm run dev
   ```

### 临时解决方案

如果代理问题持续存在，可以临时直接访问API：

```javascript
// 在 src/utils/request.js 中临时修改baseURL
const config = {
  baseURL: 'http://**************:3011/api', // 直接访问API服务器
  timeout: 60000
}
```

**注意**: 这种方式会遇到CORS问题，仅用于紧急情况。

## 监控和维护

### 定期检查项目
1. 代理服务健康状态
2. API响应时间
3. 错误日志记录
4. 网络连接稳定性

### 性能优化建议
1. 考虑在后端实现API缓存
2. 优化数据库查询性能
3. 使用CDN加速静态资源
4. 实现API分页减少单次数据量

## 文件清单

### 修改的文件
- `webpack.config.js` - 代理配置优化
- `src/utils/request.js` - 请求重试机制
- `src/api/index.js` - API特殊配置

### 新增的文件
- `src/utils/errorHandler.js` - 错误处理工具
- `src/utils/proxyHealthCheck.js` - 代理健康检查
- `src/components/ProxyStatus.vue` - 代理状态监控组件
- `src/examples/ApiErrorHandlingExample.vue` - 使用示例
- `test-proxy.js` - 代理测试脚本

## 总结

通过以上优化，项目现在具备了：
- ✅ 更长的超时时间设置
- ✅ 自动重试机制
- ✅ 代理健康监控
- ✅ 友好的错误提示
- ✅ 详细的日志记录
- ✅ 测试和监控工具

这些改进应该能显著减少504代理错误的发生，并提供更好的用户体验。
